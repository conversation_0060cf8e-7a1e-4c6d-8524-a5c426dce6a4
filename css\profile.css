/* User Profile Styles */

/* Profile Layout */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.profile-details h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-details p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.profile-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1.5rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Profile Navigation */
.profile-nav {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: var(--admin-card-shadow);
}

.profile-nav-list {
    display: flex;
    gap: 0.5rem;
    list-style: none;
    flex-wrap: wrap;
}

.profile-nav-item {
    flex: 1;
    min-width: 120px;
}

.profile-nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--admin-transition);
    border: 1px solid transparent;
}

.profile-nav-link:hover {
    background: var(--section-bg);
    color: var(--primary-color);
}

.profile-nav-link.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Profile Content */
.profile-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.profile-main {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: var(--admin-card-shadow);
}

.profile-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.sidebar-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--admin-card-shadow);
}

.sidebar-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
}

/* Profile Forms */
.profile-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
}

/* Avatar Upload */
.avatar-upload {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    object-fit: cover;
    background: var(--section-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
}

.avatar-upload-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.avatar-upload-btn:hover {
    background: #6a1b9a;
}

/* Order History */
.order-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    transition: var(--admin-transition);
}

.order-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(75, 0, 130, 0.1);
}

.order-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.order-number {
    font-weight: 600;
    color: var(--text-color);
}

.order-date {
    color: var(--text-light);
    font-size: 0.875rem;
}

.order-products {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.order-product {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: var(--section-bg);
    border-radius: 0.5rem;
    flex: 1;
    min-width: 200px;
}

.product-image {
    width: 50px;
    height: 50px;
    border-radius: 0.25rem;
    object-fit: cover;
    background: var(--border-color);
}

.product-info h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.product-info p {
    font-size: 0.75rem;
    color: var(--text-light);
}

.order-footer {
    display: flex;
    justify-content: between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.order-total {
    font-weight: 600;
    color: var(--text-color);
}

/* Settings Sections */
.settings-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.section-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* File Upload Styles */
.file-upload {
    position: relative;
    display: inline-block;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.file-upload-label:hover {
    background: #6a1b9a;
}

/* Form Validation Styles */
.form-input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

.success-message {
    color: var(--success-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        padding: 1rem;
    }

    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .profile-details h1 {
        font-size: 2rem;
    }

    .profile-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .profile-nav-list {
        flex-direction: column;
    }

    .profile-nav-item {
        min-width: auto;
    }

    .profile-content {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .avatar-upload {
        flex-direction: column;
        text-align: center;
    }

    .order-products {
        flex-direction: column;
    }

    .order-product {
        min-width: auto;
    }

    .order-header,
    .order-footer {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
