<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal</title>
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
        }

        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-primary {
            background: #4B0082;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Modal Test Page</h1>
    
    <button class="btn btn-primary" onclick="openModal()">Open Modal</button>
    <button class="btn btn-secondary" onclick="alert('Test button works!')">Test Alert</button>

    <!-- Modal -->
    <div class="modal" id="testModal" style="display: none;">
        <div class="modal-content">
            <h3>Test Modal</h3>
            <p>This is a test modal to verify functionality.</p>
            <button class="btn btn-secondary" onclick="closeModal()">Close</button>
        </div>
    </div>

    <script>
        console.log('Test page loaded');

        function openModal() {
            console.log('Opening modal...');
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('Modal opened');
            } else {
                console.error('Modal not found');
            }
        }

        function closeModal() {
            console.log('Closing modal...');
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'none';
                console.log('Modal closed');
            }
        }

        // Test if JavaScript is working
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
        });
    </script>
</body>
</html>
