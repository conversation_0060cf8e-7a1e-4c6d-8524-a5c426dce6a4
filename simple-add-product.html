<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Add Product</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        textarea {
            resize: vertical;
            height: 80px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        .btn-primary {
            background: #4B0082;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .form-actions {
            text-align: center;
            margin-top: 30px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .products-list {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #eee;
        }

        .product-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #4B0082;
        }

        .product-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .product-details {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ Add New Product</h1>
        
        <div id="message"></div>

        <form id="productForm">
            <div class="form-group">
                <label for="name">Product Name *</label>
                <input type="text" id="name" required>
            </div>

            <div class="form-group">
                <label for="price">Price *</label>
                <input type="number" id="price" step="0.01" min="0" required>
            </div>

            <div class="form-group">
                <label for="category">Category *</label>
                <select id="category" required>
                    <option value="">Select Category</option>
                    <option value="men">Men</option>
                    <option value="women">Women</option>
                </select>
            </div>

            <div class="form-group">
                <label for="stock">Stock Quantity *</label>
                <input type="number" id="stock" min="0" required>
            </div>

            <div class="form-group">
                <label for="image">Image URL</label>
                <input type="url" id="image" placeholder="https://example.com/image.jpg">
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" placeholder="Product description..."></textarea>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Add Product</button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">Clear Form</button>
            </div>
        </form>

        <div class="products-list">
            <h2>Added Products</h2>
            <div id="productsList">
                <p style="color: #666; text-align: center;">No products added yet.</p>
            </div>
        </div>
    </div>

    <script>
        // Simple product management
        let products = JSON.parse(localStorage.getItem('vaith_products')) || [];

        // Display existing products on page load
        displayProducts();

        // Form submission
        document.getElementById('productForm').addEventListener('submit', function(e) {
            e.preventDefault();
            addProduct();
        });

        function addProduct() {
            // Get form values
            const name = document.getElementById('name').value.trim();
            const price = parseFloat(document.getElementById('price').value);
            const category = document.getElementById('category').value;
            const stock = parseInt(document.getElementById('stock').value);
            const image = document.getElementById('image').value.trim();
            const description = document.getElementById('description').value.trim();

            // Validate
            if (!name || !price || !category || stock < 0) {
                showMessage('Please fill in all required fields.', 'error');
                return;
            }

            // Create product
            const product = {
                id: Date.now(),
                name: name,
                brand: 'VAITH',
                description: description || 'No description available',
                category: category,
                status: 'active',
                sku: 'SKU-' + Date.now(),
                price: price,
                originalPrice: null,
                stock: stock,
                images: [image || 'https://via.placeholder.com/400x500?text=No+Image'],
                sizes: ['One Size'],
                colors: ['Default'],
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            // Save to localStorage
            products.push(product);
            localStorage.setItem('vaith_products', JSON.stringify(products));

            // Show success message
            showMessage(`Product "${name}" added successfully!`, 'success');

            // Clear form and refresh display
            clearForm();
            displayProducts();
        }

        function clearForm() {
            document.getElementById('productForm').reset();
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${text}</div>`;
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }

        function displayProducts() {
            const container = document.getElementById('productsList');
            
            if (products.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center;">No products added yet.</p>';
                return;
            }

            container.innerHTML = products.map(product => `
                <div class="product-item">
                    <div class="product-name">${product.name}</div>
                    <div class="product-details">
                        Price: $${product.price} | Category: ${product.category} | Stock: ${product.stock} | SKU: ${product.sku}
                    </div>
                </div>
            `).join('');
        }

        console.log('Simple Add Product page loaded successfully!');
        console.log('Current products:', products.length);
    </script>
</body>
</html>
