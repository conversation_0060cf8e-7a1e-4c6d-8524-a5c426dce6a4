<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Remove outlines from all elements */
        *:focus,
        *:active,
        *:focus-visible {
            outline: none !important;
        }

        body {
            padding-top: 80px;
        }
        
        .products-hero {
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .products-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(216, 191, 216, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(216, 191, 216, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .products-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            /* Enhanced background styling for "Products Collection" */
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(216, 191, 216, 0.25));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 1.5rem 2.5rem;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            display: inline-block;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .products-hero h1:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .products-hero h1::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .products-hero h1:hover::before {
            opacity: 1;
        }
        
        .products-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .category-tabs {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 80px;
            z-index: 100;
        }
        
        .tabs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .tab-buttons {
            display: flex;
            justify-content: center;
            gap: 0;
            border-bottom: 1px solid #eee;
        }
        
        .tab-btn {
            padding: 1.5rem 2rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-btn.active {
            color: #4B0082;
            border-bottom-color: #4B0082;
            background: #f8f9fa;
        }
        
        .tab-btn:hover {
            color: #4B0082;
            background: #f8f9fa;
        }
        
        .filters-section {
            background: var(--section-bg);
            padding: 2rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        
        .filters-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .filters-left {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .filter-group label {
            font-weight: 500;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--input-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
        }
        

        
        .products-content {
            padding: 3rem 0;
        }

        @media (max-width: 768px) {
            .products-content {
                padding: 2rem 0;
            }
        }

        @media (max-width: 480px) {
            .products-content {
                padding: 1.5rem 0;
            }
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .category-intro {
            text-align: center;
            margin-bottom: 3rem;
            padding: 0 1rem;
        }

        .category-intro h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .category-intro p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
            transition: color 0.3s ease;
        }

        @media (max-width: 768px) {
            .category-intro h2 {
                font-size: 2rem;
            }

            .category-intro p {
                font-size: 1rem;
            }
        }
        
        .results-info {
            padding: 1rem 0;
            color: var(--text-light);
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 1.5rem;
        }

        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 1rem;
            }
        }
        
        .products-grid.list-view {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .loading-products {
            text-align: center;
            padding: 3rem 0;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .loading-spinner {
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: var(--section-bg);
            border-color: var(--primary-color);
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive styling for Products Collection heading */
        @media (max-width: 768px) {
            .products-hero h1 {
                font-size: 2.2rem;
                padding: 1rem 1.5rem;
                border-radius: 15px;
                margin-bottom: 0.8rem;
            }

            .products-hero h1::before {
                border-radius: 15px;
            }
        }

        @media (max-width: 480px) {
            .products-hero h1 {
                font-size: 1.8rem;
                padding: 0.8rem 1.2rem;
                border-radius: 12px;
                margin-bottom: 0.6rem;
            }

            .products-hero h1::before {
                border-radius: 12px;
            }
        }

        /* Entrance animation for the heading */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .products-hero h1 {
            animation: slideInUp 0.8s ease-out;
        }

        /* Enhanced accessibility - ensure proper contrast */
        @media (prefers-reduced-motion: reduce) {
            .products-hero h1 {
                animation: none;
                transition: none;
            }

            .products-hero h1:hover {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="90" height="40" viewBox="0 0 90 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">VAITH</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="page-hero">
        <div class="page-hero-content">
            <h1>Discover fashion for everyone - Women & Men</h1>
            <p>Explore our curated collection of trendy and timeless pieces</p>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="filters-left">
                    <div class="filter-group">
                        <label for="categoryFilter">Category:</label>
                        <select id="categoryFilter" class="filter-select">
                            <option value="all">All Categories</option>
                            <option value="women">Women</option>
                            <option value="men">Men</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sortFilter">Sort by:</label>
                        <select id="sortFilter" class="filter-select">
                            <option value="featured">Featured</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="newest">Newest</option>
                            <option value="popular">Most Popular</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Content -->
    <section class="products-content">
        <div class="container">
            <div class="category-intro">
                <h2 id="categoryTitle">All Products</h2>
                <p id="categoryDescription">Discover our complete collection of fashion items for everyone.</p>
            </div>

            <div class="results-info">
                <span id="resultsCount">Loading products...</span>
            </div>

            <div class="loading-products" id="productsLoading">
                <div class="loading-spinner"></div>
                <p>Loading products...</p>
            </div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded dynamically -->
            </div>

            <div class="pagination" id="productsPagination">
                <!-- Pagination will be generated dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>VAITH</h3>
                    <p>Your destination for trendy and affordable fashion.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Shipping Info</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 VAITH. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div class="modal" id="cartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Shopping Cart</h3>
                <button class="close-modal" id="closeCart">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="cartItems">
                <!-- Cart items will be loaded dynamically -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <strong>Total: $<span id="cartTotal">0.00</span></strong>
                </div>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Favorites Modal -->
    <div class="modal" id="favoritesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Your Favorites</h3>
                <button class="close-modal" id="closeFavorites">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="favoritesItems">
                <!-- Favorites will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>

    <script>
        // Products page specific functionality
        let currentPage = 1;
        let currentFilters = {
            category: '',
            sort: 'featured'
        };

        // Products data - will be loaded from admin system
        let productsData = {
            women: [],
            men: [],
            all: []
        };

        // File protocol compatible product loading
        function loadProductsFromAdmin() {
            console.log('Loading products for file:// protocol compatibility');

            // Try to load from localStorage directly (works with file:// protocol)
            try {
                const storedProducts = localStorage.getItem('vaith_products');
                if (storedProducts) {
                    const adminProducts = JSON.parse(storedProducts);
                    console.log(`Found ${adminProducts.length} products in localStorage`);

                    // Clear existing data
                    productsData = { women: [], men: [], all: [] };

                // Convert admin product format to frontend format and categorize
                adminProducts.forEach(product => {
                    const frontendProduct = {
                        id: product.id,
                        title: product.name,
                        price: product.price,
                        originalPrice: product.originalPrice,
                        image: product.images && product.images.length > 0 ? product.images[0] : 'https://via.placeholder.com/400x500?text=No+Image',
                        rating: product.rating || 0,
                        reviews: product.reviews || 0,
                        category: product.category,
                        subcategory: product.subcategory || 'general',
                        onSale: product.originalPrice && product.originalPrice > product.price
                    };

                    // Add to appropriate category
                    if (product.category === 'women') {
                        productsData.women.push(frontendProduct);
                    } else if (product.category === 'men') {
                        productsData.men.push(frontendProduct);
                    }

                    // Add to all products
                    productsData.all.push(frontendProduct);
                });

                console.log(`Loaded products: Women: ${productsData.women.length}, Men: ${productsData.men.length}, Total: ${productsData.all.length}`);
                } else {
                    console.log('No products found in localStorage');
                }
            } catch (error) {
                console.error('Error loading products from localStorage:', error);
                productsData = { women: [], men: [], all: [] };
            }
        }



        document.addEventListener('DOMContentLoaded', function() {
            initializeProductsPage();
        });

        function initializeProductsPage() {
            // Load products from admin system first
            loadProductsFromAdmin();

            // Check for URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const categoryParam = urlParams.get('category');

            if (categoryParam) {
                currentFilters.category = categoryParam;
                document.getElementById('categoryFilter').value = categoryParam;
                updatePageContent();
            }

            // Initialize filters
            initializeFilters();

            // Load initial products
            loadProducts();
        }

        function initializeFilters() {
            document.getElementById('categoryFilter').addEventListener('change', handleFilterChange);
            document.getElementById('sortFilter').addEventListener('change', handleFilterChange);
        }

        function handleFilterChange() {
            const categoryFilter = document.getElementById('categoryFilter');
            currentFilters.category = categoryFilter.value;
            currentFilters.sort = document.getElementById('sortFilter').value;
            currentPage = 1;

            // Update page title and description
            updatePageContent();

            // Load products with new filters
            loadProducts();
        }

        function updatePageContent() {
            const categoryTitle = document.getElementById('categoryTitle');
            const categoryDescription = document.getElementById('categoryDescription');

            if (currentFilters.category === 'all') {
                categoryTitle.textContent = "All Products";
                categoryDescription.textContent = "Discover our complete collection of fashion items for everyone.";
            } else if (currentFilters.category === 'women') {
                categoryTitle.textContent = "Women's Fashion";
                categoryDescription.textContent = "Discover the latest trends in women's clothing. From casual wear to elegant dresses, find your perfect style.";
            } else if (currentFilters.category === 'men') {
                categoryTitle.textContent = "Men's Fashion";
                categoryDescription.textContent = "Explore our collection of men's clothing. From classic shirts to modern streetwear, find your signature look.";
            }
        }



        function loadProducts() {
            const loadingElement = document.getElementById('productsLoading');
            const gridElement = document.getElementById('productsGrid');

            // Show loading
            loadingElement.style.display = 'block';
            gridElement.style.display = 'none';

            // Simulate API call
            setTimeout(() => {
                const filteredProducts = getFilteredProducts();
                renderProducts(filteredProducts);
                updateResultsInfo(filteredProducts.length);
                renderPagination(filteredProducts.length);

                // Hide loading
                loadingElement.style.display = 'none';
                gridElement.style.display = 'grid';
            }, 500);
        }

        function getFilteredProducts() {
            let allProducts = [];

            // Get products based on category filter
            if (currentFilters.category && currentFilters.category !== 'all') {
                allProducts = [...(productsData[currentFilters.category] || [])];
            } else {
                // Get all products
                allProducts = [...productsData.all];
            }

            // Sort products
            switch (currentFilters.sort) {
                case 'price-low':
                    allProducts.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    allProducts.sort((a, b) => b.price - a.price);
                    break;
                case 'newest':
                    allProducts.reverse();
                    break;
                case 'popular':
                    allProducts.sort((a, b) => b.reviews - a.reviews);
                    break;
                default:
                    // Featured - keep original order
                    break;
            }

            return allProducts;
        }

        function renderProducts(products) {
            const container = document.getElementById('productsGrid');
            container.innerHTML = '';

            if (products.length === 0) {
                const hasFilters = currentCategory !== 'all' || currentSort !== 'featured';
                container.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--text-light);">
                        <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h3 style="margin-bottom: 0.5rem; color: var(--text-color);">
                            ${hasFilters ? 'No Products Found' : 'No Products Available'}
                        </h3>
                        <p style="margin-bottom: 1.5rem;">
                            ${hasFilters
                                ? 'No products match your current filters. Try adjusting your search criteria.'
                                : 'Products will appear here once they are added through the admin panel.'}
                        </p>
                        ${hasFilters
                            ? '<button class="btn btn-primary" onclick="clearFilters()">Clear Filters</button>'
                            : '<a href="index.html" class="btn btn-primary">Back to Home</a>'}
                    </div>
                `;
                return;
            }

            const itemsPerPage = 12;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = products.slice(startIndex, endIndex);

            pageProducts.forEach(product => {
                const productCard = createProductCard(product);
                container.appendChild(productCard);
            });
        }

        function createProductCard(product) {
            const card = document.createElement('div');
            card.className = 'product-card';
            card.setAttribute('data-product-id', product.id);

            const discount = product.originalPrice ?
                Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

            card.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.title} - ${product.category} ${product.subcategory} with ${product.rating} star rating" loading="lazy">
                    <div class="product-actions">
                        <button class="action-btn favorite-btn" data-product-id="${product.id}" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="action-btn quick-view-btn" data-product-id="${product.id}" title="Quick View">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    ${product.onSale && discount > 0 ? `<div class="discount-badge">-${discount}%</div>` : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <div class="product-price">
                        <span class="current-price">$${product.price}</span>
                        ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
                    </div>
                    <div class="product-rating">
                        <div class="stars">
                            ${generateStars(product.rating)}
                        </div>
                        <span class="rating-count">(${product.reviews})</span>
                    </div>
                    <div class="product-card-actions">
                        <button class="btn btn-primary add-to-cart-btn" data-product-id="${product.id}">
                            <i class="fas fa-shopping-cart"></i>
                            Add to Cart
                        </button>
                    </div>
                </div>
            `;

            // Add event listeners
            const favoriteBtn = card.querySelector('.favorite-btn');
            const addToCartBtn = card.querySelector('.add-to-cart-btn');
            const quickViewBtn = card.querySelector('.quick-view-btn');

            favoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(product.id);
            });

            addToCartBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                addToCart(product);
            });

            quickViewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                window.location.href = `product.html?id=${product.id}`;
            });

            // Navigate to product page on card click
            card.addEventListener('click', () => {
                window.location.href = `product.html?id=${product.id}`;
            });

            return card;
        }

        function updateResultsInfo(totalResults) {
            const resultsElement = document.getElementById('resultsCount');
            resultsElement.textContent = `Showing ${totalResults} products`;
        }

        function renderPagination(totalItems) {
            const container = document.getElementById('productsPagination');
            const itemsPerPage = 12;
            const totalPages = Math.ceil(totalItems / itemsPerPage);

            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            paginationHTML += `
                <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span>...</span>';
                }
            }

            // Next button
            paginationHTML += `
                <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            container.innerHTML = paginationHTML;
        }

        function changePage(page) {
            currentPage = page;
            loadProducts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Make changePage available globally
        window.changePage = changePage;

        // Clear filters function
        function clearFilters() {
            currentCategory = 'all';
            currentSort = 'featured';
            document.getElementById('categoryFilter').value = 'all';
            document.getElementById('sortFilter').value = 'featured';
            updatePageContent();
            loadProducts();
        }

        // Make clearFilters available globally
        window.clearFilters = clearFilters;
    </script>
</body>
</html>
