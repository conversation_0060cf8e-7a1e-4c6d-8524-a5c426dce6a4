<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Product Data - VAITH Admin</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        body {
            background: var(--admin-bg);
            color: var(--admin-text-primary);
            font-family: 'Inter', sans-serif;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .clear-container {
            background: var(--admin-card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--admin-shadow-lg);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .clear-icon {
            font-size: 4rem;
            color: var(--admin-danger);
            margin-bottom: 1rem;
        }
        
        .clear-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--admin-text-primary);
        }
        
        .clear-description {
            color: var(--admin-text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .clear-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .status-message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--admin-success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .status-info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--admin-primary);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body>
    <div class="clear-container">
        <div class="clear-icon">
            <i class="fas fa-trash-alt"></i>
        </div>
        
        <h1 class="clear-title">Clear Product Data</h1>
        
        <p class="clear-description">
            This will remove all existing product data from the system, giving you a clean slate to start adding new products through the admin panel.
        </p>
        
        <div class="clear-actions">
            <button class="btn btn-secondary" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
            <button class="btn btn-danger" onclick="clearProductData()">
                <i class="fas fa-trash"></i> Clear All Products
            </button>
        </div>
        
        <div id="statusMessage" style="display: none;"></div>
    </div>

    <script>
        function clearProductData() {
            try {
                // Clear product data from localStorage
                localStorage.removeItem('vaith_products');
                
                // Show success message
                showStatus('All product data has been cleared successfully! You can now add new products through the admin panel.', 'success');
                
                // Update button
                const clearBtn = document.querySelector('.btn-danger');
                clearBtn.innerHTML = '<i class="fas fa-check"></i> Data Cleared';
                clearBtn.disabled = true;
                clearBtn.style.opacity = '0.6';
                
                // Add navigation buttons
                setTimeout(() => {
                    const actionsDiv = document.querySelector('.clear-actions');
                    actionsDiv.innerHTML = `
                        <a href="admin-dashboard.html" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                        </a>
                        <a href="admin-products.html" class="btn btn-secondary">
                            <i class="fas fa-plus"></i> Add Products
                        </a>
                    `;
                }, 1000);
                
            } catch (error) {
                console.error('Error clearing product data:', error);
                showStatus('Error clearing product data. Please try again.', 'error');
            }
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = `status-message status-${type}`;
            statusDiv.style.display = 'block';
        }
        
        function goBack() {
            window.history.back();
        }
        
        // Check current product count on load
        document.addEventListener('DOMContentLoaded', function() {
            const products = localStorage.getItem('vaith_products');
            const productCount = products ? JSON.parse(products).length : 0;
            
            if (productCount === 0) {
                showStatus(`No products found in the system. The product data is already clean.`, 'info');
                
                const clearBtn = document.querySelector('.btn-danger');
                clearBtn.innerHTML = '<i class="fas fa-check"></i> Already Clean';
                clearBtn.disabled = true;
                clearBtn.style.opacity = '0.6';
                
                // Show navigation buttons
                const actionsDiv = document.querySelector('.clear-actions');
                actionsDiv.innerHTML = `
                    <a href="admin-dashboard.html" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                    </a>
                    <a href="admin-products.html" class="btn btn-secondary">
                        <i class="fas fa-plus"></i> Add Products
                    </a>
                `;
            } else {
                showStatus(`Found ${productCount} product(s) in the system.`, 'info');
            }
        });
    </script>
</body>
</html>
