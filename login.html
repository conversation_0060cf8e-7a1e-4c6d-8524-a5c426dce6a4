<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Remove outlines from all elements */
        *:focus,
        *:active,
        *:focus-visible {
            outline: none !important;
        }

        body {
            padding-top: 80px;
            background: linear-gradient(135deg, #4B0082 0%, #D8BFD8 100%);
            min-height: 100vh;
            transition: background 0.3s ease;
        }

        /* Dark mode background for login page */
        [data-theme="dark"] body,
        body.dark-theme {
            background: linear-gradient(135deg, #0f0f0f 0%, #18181b 25%, #27272a 50%, #3f3f46 75%, #52525b 100%);
        }
        
        .auth-container {
            max-width: 400px;
            margin: 2rem auto;
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        /* Dark mode auth container */
        [data-theme="dark"] .auth-container,
        body.dark-theme .auth-container {
            background: var(--card-bg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            border: 1px solid var(--border-color);
        }
        
        .auth-header {
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-header h1 {
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }
        
        .auth-header p {
            opacity: 0.9;
        }
        
        .auth-form {
            padding: 2rem;
            background: var(--card-bg);
            transition: background-color 0.3s ease;
        }

        .form-group {
            margin-bottom: var(--form-element-margin);
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .form-group input {
            width: 100%;
            padding: var(--form-input-padding);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            background: var(--input-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
        }
        
        .form-group.error input {
            border-color: #e74c3c;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            display: none;
        }
        
        .form-group.error .error-message {
            display: block;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--form-element-margin);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .checkbox-group label {
            color: var(--text-color);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            text-decoration: underline;
            color: #6a1b9a;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(75, 0, 130, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
            transition: background-color 0.3s ease;
        }

        .divider span {
            background: var(--card-bg);
            padding: 0 1rem;
            transition: background-color 0.3s ease;
        }
        
        .social-login {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .social-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .social-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: var(--section-bg);
        }
        
        .auth-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: var(--text-light);
            background: var(--card-bg);
            transition: all 0.3s ease;
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            text-decoration: underline;
            color: #6a1b9a;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 1rem 0;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4B0082;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="90" height="40" viewBox="0 0 90 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">VAITH</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Login Form -->
    <div class="auth-container">
        <div class="auth-header">
            <h1>Welcome Back</h1>
            <p>Sign in to your account</p>
        </div>
        
        <form class="auth-form" id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
                <div class="error-message" id="emailError"></div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
                <div class="error-message" id="passwordError"></div>
            </div>
            
            <div class="form-options">
                <div class="checkbox-group">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">Remember me</label>
                </div>
                <a href="#" class="forgot-password">Forgot password?</a>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                Sign In
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Signing you in...</p>
            </div>
        </form>
        
        <div class="divider">
            <span>or continue with</span>
        </div>
        
        <div class="social-login">
            <button class="social-btn" id="googleLogin">
                <i class="fab fa-google"></i>
                Google
            </button>
            <button class="social-btn" id="facebookLogin">
                <i class="fab fa-facebook-f"></i>
                Facebook
            </button>
        </div>
        
        <div class="auth-footer">
            <div style="background: var(--section-bg); padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; border: 1px solid var(--border-color);">
                <h4 style="margin: 0 0 0.5rem 0; color: var(--text-color); font-size: 0.875rem;">Demo Credentials:</h4>
                <div style="font-size: 0.75rem; color: var(--text-light); line-height: 1.4;">
                    <strong>Admin:</strong> <EMAIL> / admin123<br>
                    <strong>User:</strong> <EMAIL> / user123
                </div>
            </div>
            <p>Don't have an account? <a href="signup.html">Sign up here</a></p>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    <script src="js/validation.js"></script>
    
    <script>
        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            if (authManager.isLoggedIn()) {
                const user = authManager.getCurrentUser();
                if (user.role === 'admin') {
                    window.location.href = 'admin-dashboard.html';
                } else {
                    window.location.href = 'user-profile.html';
                }
                return;
            }
        });

        // Login form functionality
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            // Clear previous errors
            clearErrors();

            // Validate form
            if (!validateLoginForm(email, password)) {
                return;
            }

            // Show loading state
            showLoading(true);

            // Attempt login
            setTimeout(async () => {
                try {
                    const user = await authManager.login(email, password);

                    if (remember) {
                        localStorage.setItem('rememberLogin', 'true');
                    }

                    showNotification('Login successful!');

                    // Redirect based on user role
                    setTimeout(() => {
                        if (user.role === 'admin') {
                            window.location.href = 'admin-dashboard.html';
                        } else {
                            window.location.href = 'user-profile.html';
                        }
                    }, 1000);

                } catch (error) {
                    showError(error.message);
                } finally {
                    showLoading(false);
                }
            }, 1000);
        });
        
        function validateLoginForm(email, password) {
            let isValid = true;
            
            if (!email || !isValidEmail(email)) {
                showFieldError('email', 'Please enter a valid email address');
                isValid = false;
            }
            
            if (!password || password.length < 6) {
                showFieldError('password', 'Password must be at least 6 characters');
                isValid = false;
            }
            
            return isValid;
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showFieldError(fieldName, message) {
            const field = document.getElementById(fieldName);
            const errorElement = document.getElementById(fieldName + 'Error');
            
            field.parentElement.classList.add('error');
            errorElement.textContent = message;
        }
        
        function clearErrors() {
            const errorGroups = document.querySelectorAll('.form-group.error');
            errorGroups.forEach(group => {
                group.classList.remove('error');
                group.querySelector('.error-message').textContent = '';
            });
        }
        
        function showError(message) {
            // Create notification element for errors
            const notification = document.createElement('div');
            notification.className = 'notification error';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: #e74c3c;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 1003;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Remove notification after 4 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
        
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            
            if (show) {
                loading.style.display = 'block';
                loginBtn.disabled = true;
            } else {
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        }
        
        // Social login handlers
        document.getElementById('googleLogin').addEventListener('click', function() {
            showNotification('Google login would be implemented here');
        });
        
        document.getElementById('facebookLogin').addEventListener('click', function() {
            showNotification('Facebook login would be implemented here');
        });
        

    </script>
</body>
</html>
